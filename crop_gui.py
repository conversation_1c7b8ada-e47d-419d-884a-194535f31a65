#!/usr/bin/env python3
"""
GUI for PDF Auto-Cropper for E-ink Devices
Provides a simple interface for the PDF cropping functionality.
"""

import os
import sys
import platform
import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import threading
from pathlib import Path
import io
from contextlib import redirect_stdout

# Fix for macOS Tcl/Tk issues
if platform.system() == "Darwin":
    # Try to locate and set the Tcl/Tk library path
    tcl_tk_paths = [
        "/Library/Frameworks/Tcl.framework/Versions/Current",
        "/Library/Frameworks/Tk.framework/Versions/Current",
        "/System/Library/Frameworks/Tcl.framework/Versions/Current",
        "/System/Library/Frameworks/Tk.framework/Versions/Current",
        "/opt/homebrew/opt/tcl-tk/lib",  # Homebrew path on Apple Silicon
        "/Users/<USER>/.local/share/uv/python/cpython-3.12.0-macos-aarch64-none/lib/tcl8.6",
    ]
    
    for path in tcl_tk_paths:
        if os.path.exists(path):
            os.environ["TCL_LIBRARY"] = os.path.join(path, "Tcl")
            os.environ["TK_LIBRARY"] = os.path.join(path, "Tk")
            break

# Import the crop functionality
from crop import crop_pdf

class RedirectText:
    """Redirect stdout to a tkinter Text widget"""
    def __init__(self, text_widget):
        self.text_widget = text_widget
        self.buffer = io.StringIO()

    def write(self, string):
        self.buffer.write(string)
        self.text_widget.delete(1.0, tk.END)
        self.text_widget.insert(tk.END, self.buffer.getvalue())
        self.text_widget.see(tk.END)
        self.text_widget.update()

    def flush(self):
        pass

class PDFCropperApp:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF Auto-Cropper for E-ink Devices")
        self.root.geometry("600x500")
        
        self.input_file = tk.StringVar()
        self.output_file = tk.StringVar()
        self.buffer = tk.DoubleVar(value=0.01)
        self.footer_height = tk.DoubleVar(value=0.1)
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Input file selection
        ttk.Label(main_frame, text="Input PDF:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.input_file, width=50).grid(row=0, column=1, pady=5)
        ttk.Button(main_frame, text="Browse...", command=self.browse_input).grid(row=0, column=2, padx=5, pady=5)
        
        # Output file selection
        ttk.Label(main_frame, text="Output PDF:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_file, width=50).grid(row=1, column=1, pady=5)
        ttk.Button(main_frame, text="Browse...", command=self.browse_output).grid(row=1, column=2, padx=5, pady=5)
        
        # Options frame
        options_frame = ttk.LabelFrame(main_frame, text="Options", padding="10")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=tk.EW, pady=10)
        
        # Buffer option
        ttk.Label(options_frame, text="Buffer space:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Scale(options_frame, from_=0.0, to=0.1, variable=self.buffer, 
                 orient=tk.HORIZONTAL, length=200).grid(row=0, column=1, sticky=tk.W, pady=5)
        ttk.Label(options_frame, textvariable=tk.StringVar(value=lambda: f"{self.buffer.get():.2f}")).grid(row=0, column=2, sticky=tk.W, pady=5)
        
        # Footer height option
        ttk.Label(options_frame, text="Footer height:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Scale(options_frame, from_=0.05, to=0.2, variable=self.footer_height, 
                 orient=tk.HORIZONTAL, length=200).grid(row=1, column=1, sticky=tk.W, pady=5)
        ttk.Label(options_frame, textvariable=tk.StringVar(value=lambda: f"{self.footer_height.get():.2f}")).grid(row=1, column=2, sticky=tk.W, pady=5)
        
        # Process button
        ttk.Button(main_frame, text="Process PDF", command=self.process_pdf).grid(row=3, column=0, columnspan=3, pady=10)
        
        # Output log
        log_frame = ttk.LabelFrame(main_frame, text="Log", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=tk.NSEW, pady=5)
        main_frame.rowconfigure(4, weight=1)
        
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Redirect stdout to the log text widget
        self.redirect = RedirectText(self.log_text)
        
    def browse_input(self):
        filename = filedialog.askopenfilename(
            title="Select PDF file",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.input_file.set(filename)
            # Auto-set output filename
            input_path = Path(filename)
            default_output = input_path.parent / f"{input_path.stem}-cropped.pdf"
            self.output_file.set(str(default_output))
    
    def browse_output(self):
        filename = filedialog.asksaveasfilename(
            title="Save PDF file",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.output_file.set(filename)
    
    def process_pdf(self):
        input_path = self.input_file.get()
        output_path = self.output_file.get()
        
        if not input_path:
            messagebox.showerror("Error", "Please select an input PDF file.")
            return
        
        if not output_path:
            messagebox.showerror("Error", "Please specify an output PDF file.")
            return
        
        # Clear log
        self.log_text.delete(1.0, tk.END)
        
        # Run processing in a separate thread to keep UI responsive
        threading.Thread(target=self._run_processing, daemon=True).start()
    
    def _run_processing(self):
        try:
            # Redirect stdout to capture print statements from crop_pdf
            sys.stdout = self.redirect
            
            crop_pdf(
                self.input_file.get(),
                self.output_file.get(),
                buffer=self.buffer.get(),
                footer_height_ratio=self.footer_height.get()
            )
            
            # Show success message
            self.root.after(0, lambda: messagebox.showinfo("Success", "PDF processing completed successfully!"))
            
        except Exception as e:
            error_msg = f"Error processing PDF: {str(e)}"
            print(error_msg)
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
        finally:
            # Restore stdout
            sys.stdout = sys.__stdout__

def main():
    root = tk.Tk()
    app = PDFCropperApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
