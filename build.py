#!/usr/bin/env python3
"""
Build script for creating executable from the PDF Cropper GUI
"""

import os
import platform
import subprocess
import sys

def build_executable():
    print("Building PDF Cropper executable...")
    
    # Determine platform-specific options
    if platform.system() == "Windows":
        icon_option = "--icon=icon.ico" if os.path.exists("icon.ico") else ""
    elif platform.system() == "Darwin":  # macOS
        icon_option = "--icon=icon.icns" if os.path.exists("icon.icns") else ""
    else:  # Linux
        icon_option = "--icon=icon.png" if os.path.exists("icon.png") else ""
    
    # Build command
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=PDFCropper",
        icon_option,
        "crop_gui.py"
    ]
    
    # Remove empty options
    cmd = [opt for opt in cmd if opt]
    
    # Run PyInstaller
    try:
        subprocess.run(cmd, check=True)
        print("Build completed successfully!")
        print(f"Executable created in {os.path.abspath('dist')} directory")
    except subprocess.CalledProcessError as e:
        print(f"Error building executable: {e}")
        sys.exit(1)

if __name__ == "__main__":
    build_executable()